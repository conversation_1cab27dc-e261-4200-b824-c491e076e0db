# Removing the old packages
npm rm @appmaker-xyz/app-config \
 @appmaker-xyz/core \
 @appmaker-xyz/plugins \
 @appmaker-xyz/react-native \
 @appmaker-xyz/shopify \
 @appmaker-xyz/themes \
 @appmaker-xyz/uikit \
 @appmaker-xyz/ui \
 @appmaker-xyz/shopify-core-theme \
 @appmaker-xyz/files

# Installing the new packages
npm install \
    @appmaker-xyz/app-config \
    @appmaker-xyz/core \
    @appmaker-xyz/plugins \
    @appmaker-xyz/react-native \
    @appmaker-xyz/shopify \
    @appmaker-xyz/themes \
    @appmaker-xyz/uikit \
    @appmaker-xyz/ui \
    @appmaker-xyz/files