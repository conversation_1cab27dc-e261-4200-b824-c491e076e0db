{"name": "@appmaker-xyz/appmaker-sdk", "version": "3.0.0-expo-update-2502.4.1117", "private": true, "scripts": {"start": "expo start --dev-client", "gql:codegen": "node node_modules/@appmaker-xyz/shopify/scripts/extended-graphql/index.js"}, "dependencies": {"@babel/preset-react": "^7.14.5", "@craco/craco": "^6.3.0", "@fluentui/react-context-selector": "^0.53.4", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.1", "@invertase/react-native-apple-authentication": "2.2.1", "@miblanchard/react-native-slider": "^2.2.0", "@native-html/iframe-plugin": "^1.1.2", "@notifee/react-native": "^7.7.1", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "1.19.3", "@react-native-clipboard/clipboard": "^1.11.2", "@react-native-community/datetimepicker": "7.2.0", "@react-native-community/picker": "^1.8.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/analytics": "^19.2.2", "@react-native-firebase/app": "^19.2.2", "@react-native-firebase/auth": "^19.2.2", "@react-native-firebase/crashlytics": "^19.2.2", "@react-native-firebase/dynamic-links": "^19.2.2", "@react-native-firebase/installations": "^19.2.2", "@react-native-firebase/messaging": "^19.2.2", "@react-native-firebase/perf": "^19.2.2", "@react-native-firebase/remote-config": "^19.2.2", "@react-native-google-signin/google-signin": "^10.0.1", "@react-native-masked-view/masked-view": "^0.2.9", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "@react-navigation/elements": "^1.3.18", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@shopify/flash-list": "1.6.3", "@tanstack/react-query": "^4.28.0", "@wordpress/hooks": "^3.1.1", "abortcontroller-polyfill": "^1.7.3", "axios": "^1.3.5", "base-64": "^0.1.0", "color": "^2.0.0", "contrast": "^1.0.1", "dayjs": "^1.11.2", "del": "^2.2.2", "ejs": "^3.1.6", "ejs-browser": "^3.2.2", "events": "^3.3.0", "expo": "^49.0.0", "expo-build-properties": "~0.8.3", "expo-constants": "~14.4.2", "expo-splash-screen": "~0.20.4", "expo-status-bar": "~1.6.0", "flowed-st": "^1.0.5", "global": "^4.3.2", "google-auth-library": "^0.10.0", "googleapis": "^19.0.0", "graphql": "^16.8.1", "html-entities": "^2.3.3", "i18next": "^22.5.1", "immer": "^9.0.6", "intl": "^1.2.5", "intl-pluralrules": "^2.0.1", "json-to-graphql-query": "^2.1.0", "lodash": "^4.17.15", "lottie-react-native": "5.1.6", "md5": "^2.2.1", "moment": "^2.19.1", "parse": "^1.10.1", "prop-types": "^15.7.2", "qs": "^6.10.1", "query-string": "^6.13.5", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.46.1", "react-i18next": "^12.3.1", "react-mixin": "^3.0.5", "react-native": "0.72.1", "react-native-animated-pulse": "^1.0.1", "react-native-auto-height-image": "^3.2.4", "react-native-autoheight-webview": "^1.6.4", "react-native-base64": "^0.2.1", "react-native-checkbox-field": "git+https://github.com/Appmaker-xyz/react-native-checkbox-field.git", "react-native-countdown-component": "^2.7.1", "react-native-debug-stylesheet": "^0.1.1", "react-native-default-preference": "^1.4.4", "react-native-deprecated-custom-components": "^0.1.0", "react-native-drawer": "^2.5.0", "react-native-dropdown-select-list": "^2.0.2", "react-native-element-dropdown": "^1.8.12", "react-native-exception-handler": "^2.9.0", "react-native-fast-image": "^8.6.3", "react-native-fbsdk-next": "^12.1.0", "react-native-gesture-handler": "2.12.1", "react-native-i18n": "2.0.14", "react-native-image-zoom-viewer": "^3.0.1", "react-native-in-app-review": "^4.3.3", "react-native-linear-gradient": "^2.6.2", "react-native-localize": "^2.2.6", "react-native-message-bar": "^1.6.0", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^15.0.1", "react-native-modalbox": "^1.7.1", "react-native-otp-verify": "^1.1.6", "react-native-pager-view": "6.2.0", "react-native-permissions": "^4.1.1", "react-native-phone-number-input": "^2.1.0", "react-native-raw-bottom-sheet": "^2.2.0", "react-native-reanimated": "~3.3.0", "react-native-render-html": "^5.1.0", "react-native-responsive-fontsize": "^0.5.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-select-input-ios": "^2.0.5", "react-native-sha256": "^1.4.7", "react-native-simple-store": "^2.0.2", "react-native-skeleton-placeholder": "^5.2.4", "react-native-snackbar": "^2.6.2", "react-native-svg": "13.9.0", "react-native-swiper": "^1.6.0", "react-native-swiper-flatlist": "^3.0.16", "react-native-tab-view": "^3.5.2", "react-native-tracking-transparency": "^0.1.1", "react-native-unistyles": "^1.0.0-rc.1", "react-native-vector-icons": "^6.6.0", "react-native-video": "^5.2.1", "react-native-web": "~0.19.6", "react-native-webview": "13.2.2", "react-query-native-devtools": "^4.0.0", "react-redux": "^5.0.4", "react-scripts": "^4.0.3", "react-timer-mixin": "^0.13.3", "react-toastify": "^8.0.3", "recyclerlistview": "^4.1.3", "redux": "^3.7.2", "redux-logger": "^2.10.2", "redux-persist": "^3.5.0", "redux-thunk": "^2.2.0", "reselect": "^2.5.4", "simpler-state": "^1.0.3", "striptags": "^3.2.0", "unescape": "^1.0.1", "url": "^0.11.0", "use-immer": "^0.8.1", "yup": "^0.32.11", "zod": "^3.20.6", "zustand": "^3.5.12", "@appmaker-xyz/app-config": "0.3.0", "@appmaker-xyz/core": "0.4.36-expo-update-d82c1e4.0", "@appmaker-xyz/plugins": "0.2.88-expo-update-d82c1e4.0", "@appmaker-xyz/react-native": "0.4.46-expo-update-d82c1e4.0", "@appmaker-xyz/remote-bundle": "0.0.6-expo-update-d82c1e4.0", "@appmaker-xyz/shopify": "0.3.68-expo-update-d82c1e4.0", "@appmaker-xyz/shopify-core-theme": "1.2.13-expo-update-d82c1e4.0", "@appmaker-xyz/themes": "0.2.30", "@appmaker-xyz/ui": "0.2.33-expo-update-d82c1e4.0", "@appmaker-xyz/uikit": "0.2.49-expo-update-d82c1e4.0", "@appmaker-xyz/files": "1.0.0", "react-native-json-tree": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.0.0", "@types/react-native": "~0.69.1", "esbuild": "^0.19.3", "expo-module-scripts": "^3.1.0", "lerna": "^6.6.1", "react-native-svg-transformer": "^1.0.0", "react-query-native-devtools": "^4.0.0", "release-it": "^15.11.0", "typescript": "^5.1.3"}, "publishConfig": {"@appmaker-xyz:registry": "https://flash.appmaker.xyz", "registry": "https://flash.appmaker.xyz"}, "workspaces": ["packages/*", "packages-dev/*"]}