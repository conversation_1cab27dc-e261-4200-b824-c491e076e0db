import { ConfigPlugin, withXcodeProject } from 'expo/config-plugins';
import { ExpoConfig, ConfigContext } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => {
  const packageName = "xyz.appmaker.runtime"
  const finalConfig = {
    ...config,
    jsEngine: 'jsc',
    name: 'Appmaker Runtime',
    slug: 'appmaker-runtime',
    version: '1.0.0',
    orientation: 'portrait',
    userInterfaceStyle: 'light',
    assetBundlePatterns: ['**/*'],
    owner: 'Appmaker',
    ios: {
      supportsTablet: true,
      config: {
        usesNonExemptEncryption: false,
      },
      bundleIdentifier: packageName,
      "infoPlist": {
        "LSApplicationQueriesSchemes": ["tez","phonepe","paytm","paytmmp","upi","itms-apps","credpay"],
        "NSUserTrackingUsageDescription": "This identifier will be used to deliver personalized ads to you."
      }
    },
    android: {
      package: packageName
    },
    plugins: [
      // '@react-native-firebase/app',
      // '@react-native-firebase/perf',
      // '@react-native-firebase/crashlytics',
      // '@react-native-firebase/crashlytics',
      "@appmaker-xyz/plugins/app-permissions/build/app.plugin.js",
      "@appmaker-xyz/plugins/build-properties/build/app.plugin.js",
      [
        "react-native-permissions",
        {
          "iosPermissions": [
            "AppTrackingTransparency",
            "Notifications"
          ]
        }
      ]
    ],
    owner: 'Appmaker',
  } as ExpoConfig;

  return finalConfig;
};