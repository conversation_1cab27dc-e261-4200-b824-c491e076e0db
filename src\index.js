import { AppmakerApp } from '@appmaker-xyz/react-native';
import React from 'react';
import { ApThemeProvider, styles } from '@appmaker-xyz/uikit';
import { setProject } from '@appmaker-xyz/app-config';
import './initApp';
import { ModalAlertProvider } from '@appmaker-xyz/ui';
import { SafeAreaProvider } from 'react-native-safe-area-context';

setProject({ id: process.env.EXPO_PUBLIC_APPMAKER_PROJECT_ID });
export default function CustomAppmakerApp() {
  return (
    <SafeAreaProvider>
      <ApThemeProvider styles={styles}>
        <ModalAlertProvider />
        <AppmakerApp />
      </ApThemeProvider>
    </SafeAreaProvider>
  );
}

